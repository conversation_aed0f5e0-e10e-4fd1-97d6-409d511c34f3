# ABOUTME: Tests for owner_work_summary API endpoint in DailyLogsController
# ABOUTME: Validates authorization and response format for company-wide work summaries

require 'rails_helper'

RSpec.describe Api::V1::DailyLogsController, type: :controller do
  let(:company) { create(:company) }
  let(:owner_user) { create(:user) }
  let(:regular_user) { create(:user) }
  
  before do
    # Create owner role
    owner_role = create(:role, name: 'owner')
    regular_role = create(:role, name: 'employee')
    
    # Assign roles
    create(:company_user_role, company: company, user: owner_user, role: owner_role)
    create(:company_user_role, company: company, user: regular_user, role: regular_role)
    
    # Create contracts
    create(:contract, company: company, user: owner_user)
    create(:contract, company: company, user: regular_user)
    
    ActsAsTenant.current_tenant = company
  end

  describe 'GET #owner_work_summary' do
    context 'when user is owner' do
      before do
        sign_in owner_user
      end

      it 'returns successful response' do
        get :owner_work_summary, params: { year: 2025, month: 1 }
        expect(response).to have_http_status(:success)
      end

      it 'returns proper JSON structure' do
        get :owner_work_summary, params: { year: 2025, month: 1 }
        json = JSON.parse(response.body)
        
        expect(json).to have_key('summary')
        expect(json).to have_key('selected_date')
        expect(json).to have_key('company_name')
        expect(json['selected_date']).to eq({ 'year' => 2025, 'month' => 1 })
      end

      it 'defaults to current month when no params given' do
        get :owner_work_summary
        json = JSON.parse(response.body)
        
        expect(json['selected_date']['year']).to eq(Date.current.year)
        expect(json['selected_date']['month']).to eq(Date.current.month)
      end
    end

    context 'when user is not owner/admin/supervisor' do
      before do
        sign_in regular_user
      end

      it 'returns forbidden status' do
        get :owner_work_summary, params: { year: 2025, month: 1 }
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'with invalid date parameters' do
      before do
        sign_in owner_user
      end

      it 'returns error for invalid year' do
        get :owner_work_summary, params: { year: 1999, month: 1 }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns error for invalid month' do
        get :owner_work_summary, params: { year: 2025, month: 13 }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
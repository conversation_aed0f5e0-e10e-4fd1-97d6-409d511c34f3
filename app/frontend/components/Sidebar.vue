<template>
  <div>
    <!-- Mobile menu overlay -->
    <div 
      v-if="isMobileMenuOpen" 
      @click="closeMobileMenu" 
      class="mobile-overlay" 
      :class="{ 'active': isMobileMenuOpen }"
      aria-hidden="true">
    </div>

    <!-- Sidebar -->
    <div 
      class="sidebar" 
      :class="{ 
        'open': isMobileMenuOpen,
        'collapsed': isCollapsed 
      }"
      data-vue-component="sidebar">
      
      <div class="sidebar-header">
        <div class="flex items-center gap-2">
          <LocalizedLink :to="rootPath" :use-anchor="true" data-testid="logo-link">
            <img :src="logoPath" alt="Týmbox Logo" :class="{ 'h-8': !isCollapsed, 'hidden': isCollapsed }" />
          </LocalizedLink>
        </div>
        <button @click="closeMobileMenu" class="p-1 rounded-md text-gray-500 md:hidden"
                data-action="close-mobile-menu" data-testid="close-mobile-menu">
          <X :size="20" />
        </button>
        <button @click="toggleSidebar" class="p-1 rounded-md hover:bg-gray-100 text-gray-500 hidden md:block"
                data-action="toggle-sidebar" data-testid="toggle-sidebar">
          <ChevronLeft v-if="!isCollapsed" :size="16" />
          <ChevronRight v-else :size="16" />
        </button>
      </div>
      
      <!-- Company info section - Desktop only -->
      <div v-if="displayCompanyName" class="company-section hidden md:block font-bold">
        <div class="company-info">
          <!-- <LocalizedLink :to="'companies'" class="company-name" :class="{ 'hidden': isCollapsed }"> -->
          <LocalizedLink :to="'companies'" :class="['nav-item company', { 'nav-item-active company': isCurrentPage(rootPath) }]" @click="handleNavItemClick">
            <div class="flex items-center gap-2">
              <!-- Company Avatar Display -->
              <div class="flex-shrink-0 w-9 h-9 flex items-center justify-center overflow-hidden bg-green-600">
                <div class="text-sm font-bold text-white text-center">
                  {{ displayCompanyName ? displayCompanyName.substring(0, 3).toUpperCase() : 'COM' }}
                </div>
              </div>

              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">
                {{ displayCompanyName }}
              </span>

              <span v-if="displayCurrentPlanName && displayCurrentPlanName !== 'free'" class="plan-badge" :class="{ 'hidden': isCollapsed }">
                <Crown :size="12" class="mr-1" />
                {{ capitalizedPlanName }}
              </span>

            </div>
          </LocalizedLink>
        </div>
      </div>
      
      <!-- Time tracking quick access -->
      <!-- <div class="time-tracker" data-component="time-tracker">
        <div class="flex flex-col" :class="{ 'hidden': isCollapsed }">
          <span class="text-sm font-medium text-gray-800">{{ $t('work_status', 'Pracovní stav') }}</span>
          <span class="text-xs text-gray-500">
            {{ isWorking ? $t('at_work', 'V práci') : $t('out_of_office', 'Mimo práci') }}
          </span>
        </div>
        <span :class="['time-toggle', isWorking ? 'time-toggle-active' : 'time-toggle-inactive']" 
              @click="toggleWorkStatus"
              data-action="toggle-work-status" 
              data-testid="work-status-toggle">
          <Clock :size="20" />
        </span>
      </div> -->
      
      <!-- Navigation -->
      <div class="sidebar-content">
        <!-- Main section -->
        <div class="nav-section">
          <div class="nav-section-title" :class="{ 'hidden': isCollapsed }">{{ $t('main_section', 'HLAVNÍ') }}</div>
          
          <LocalizedLink :to="'dashboard'" :class="['nav-item', { 'nav-item-active': isCurrentPage(rootPath) }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <LayoutDashboard :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('overview', 'Titulka') }}</span>
            </div>
          </LocalizedLink>
          
        </div>
        
        <!-- Organization section -->
        <div class="nav-section">
          <div class="nav-section-title" :class="{ 'hidden': isCollapsed }">{{ $t('your_business', 'VAŠE PODNIKÁNÍ') }}</div>
          
          <NavLink :to="localizedPath('events')" :href="eventsPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('events') }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <CalendarDays :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('agenda', 'Kalendář') }}</span>
            </div>
          </NavLink>
          
          <NavLink :to="localizedPath('works')" :href="worksPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('works') }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <LandPlot :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('works.title', 'Zakázky') }}</span>
            </div>
            <!-- <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div> -->
          </NavLink>

          <!-- <NavLink :to="localizedPath('bookings')" :href="bookingsPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('bookings') }]">
            <div class="flex items-center">
              <CalendarCheck :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('online_bookings', 'Online rezervace') }}</span>
            </div>
            <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div>
          </NavLink> -->

          <!-- <NavLink :to="localizedPath('meetings')" :href="meetingsPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('meetings') }]">
            <div class="flex items-center">
              <Handshake :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('meeting_assistant', 'Plánovač schůzek') }}</span>
            </div>
            <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div>
          </NavLink> -->


        </div>
        
        <!-- Attendance section -->
        <div class="nav-section">
          <div class="nav-section-title" :class="{ 'hidden': isCollapsed }">{{ $t('reports', 'REPORTY') }}</div>
          
          <NavLink :to="localizedPath('daily_logs/report')" :href="reportDailyLogsPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('daily_logs') && isCurrentAction('report') }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <Clock :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('monthly_report', 'Měsíční výkaz') }}</span>
            </div>
          </NavLink>
          
          <NavLink :to="localizedPath('reports/activities')" :href="reportsActivitiesPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('reports') && isCurrentAction('activities') }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <Activity :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('activities', 'Aktivity') }}</span>
            </div>
          </NavLink>
          
        </div>
        
        <!-- System section -->
        <div class="nav-section">
          <div class="nav-section-title" :class="{ 'hidden': isCollapsed }">{{ $t('organization', 'ORGANIZACE') }}</div>
          
          <NavLink v-if="isManager" :to="localizedPath('contracts')" :href="contractsPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('contracts') }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <Users :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('team', 'Tým') }}</span>
            </div>
          </NavLink>
          
          <NavLink v-if="isManager" :to="localizedPath('reports/owner-work-summary')" :link-class="['nav-item', { 'nav-item-active': isCurrentPath('reports/owner-work-summary') }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <BarChart2 :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('owner_work_summary.nav_title', 'Přehled docházky') }}</span>
            </div>
          </NavLink>
          
          <NavLink :to="localizedPath('company_connections')" :href="companyConnectionsPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('company_connections') }]" @click="handleNavItemClick">
            <div class="flex items-center ">
              <Bell :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('invitations', 'Pozvání') }}</span>
            </div>
          </NavLink>
          
          <NavLink :to="localizedPath('user_settings/edit')" :href="userSettingsPath" :link-class="['nav-item', { 'nav-item-active': isCurrentController('user_settings') }]" @click="handleNavItemClick">
            <div class="flex items-center">
              <Settings :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('personal_settings', 'Osobní nastavení') }}</span>
            </div>
            <!-- <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div> -->
          </NavLink>
        </div>
        
        <!-- Admin section - only visible to admin users -->
        <div v-if="isAdminUser" class="nav-section">
          <div class="nav-section-title" :class="{ 'hidden': isCollapsed }">{{ $t('admin.section', 'ADMIN') }}</div>
          
          <a :href="`/${currentLocale}/admin/dashboard`" :class="['nav-item admin-nav-item']">
            <div class="flex items-center">
              <Settings :size="20" class="nav-item-icon" />
              <span class="nav-item-text" :class="{ 'hidden': isCollapsed }">{{ $t('admin.panel', 'Admin Panel') }}</span>
            </div>
          </a>
        </div>
      </div>
      
      <!-- User account info -->
      <div class="sidebar-footer">
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
            {{ userInitials }}
          </div>
          <div class="ml-3 flex-grow" :class="{ 'hidden': isCollapsed }">
            <div class="text-sm font-small text-gray-800 trunc-email">{{ userEmail }}</div>
          </div>
          <button @click="handleLogout" class="cursor-pointer p-0 border-none bg-transparent" :class="{ 'hidden': isCollapsed }">
            <LogOut :size="18" class="text-gray-400 hover:text-gray-600" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { X, ChevronLeft, ChevronRight, Clock, LayoutDashboard, CalendarDays, FileText, CalendarCheck, FileBarChart2, LineChart, Handshake, Building2, Settings, LogOut, Bell, FileUser, LandPlot, Activity, Crown, Users, BarChart2 } from 'lucide-vue-next';
import authorizationMixin from '../mixins/authorizationMixin';
import axios from 'axios';
import { sendFlashMessage } from '@/utils/flashMessage';
import LocalizedLink from './LocalizedLink.vue';
import NavLink from './NavLink.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'Sidebar',
  mixins: [authorizationMixin],
  components: {
    X, ChevronLeft, ChevronRight, Clock, LayoutDashboard, CalendarDays, FileText, CalendarCheck, FileBarChart2, LineChart, Handshake, Building2, Settings, LogOut, Bell, FileUser, LandPlot, Activity, Crown, Users, BarChart2,
    LocalizedLink,
    NavLink
  },
  inject: {
    isSpaMode: {
      default: false
    }
  },
  props: {
    rootPath: { type: String, default: '/' },
    logoPath: { type: String, default: '/logo-v7a.png' },
    dailyLogsPath: { type: String, default: '/daily_logs' },
    eventsPath: { type: String, default: '/events' },
    worksPath: { type: String, default: '/works' },
    bookingsPath: { type: String, default: '/bookings' },
    meetingsPath: { type: String, default: '/meetings' },
    reportDailyLogsPath: { type: String, default: '/daily_logs/report' },
    teamSummaryDailyLogsPath: { type: String, default: '/daily_logs/team_summary' },
    contractsPath: { type: String, default: '/contracts' },
    companyConnectionsPath: { type: String, default: '/company_connections' },
    userSettingsPath: { type: String, default: '/user_settings/edit' },
    reportsActivitiesPath: { type: String, default: '/reports/activities' },
    destroyUserSessionPath: { type: String, default: '/users/sign_out' },
    currentPagePath: String,
    currentControllerName: String,
    currentActionName: String,
    userEmailProp: { type: String, default: '' }, // Renamed to avoid conflict with computed
    userRole: { type: String, default: 'employee' },
    currentPlanName: { type: String, default: 'free' },
    companyName: { type: String, default: '' },
    csrfToken: String
  },
  data() {
    return {
      isCollapsed: false,
      isMobileMenuOpen: false,
      isWorking: false,
      currentDailyLog: null,
    };
  },
  computed: {
    ...mapGetters('userStore', [
      'isAuthenticated',
      'currentUser',
      'currentCompany',
      'currentPlan',
      'hasPlusPlan',
      'hasPremiumPlan'
    ]),
    // User data - use store in SPA mode, props in legacy mode
    userEmail() {
      if (this.isSpa && this.$store) {
        return this.$store.state.userStore.email;
      }
      return this.userEmailProp;
    },
    currentUserRole() {
      if (this.isSpa && this.$store) {
        return this.$store.state.userStore.role;
      }
      return this.userRole;
    },
    currentUserPlan() {
      if (this.isSpa && this.$store) {
        return this.$store.state.userStore.currentPlan;
      }
      return this.currentPlanName;
    },
    displayCompanyName() {
      if (this.isSpa && this.$store) {
        return this.$store.state.userStore.company?.name;
      }
      return this.companyName;
    },
    displayCurrentPlanName() {
      if (this.isSpa && this.$store) {
        return this.$store.state.userStore.currentPlan;
      }
      return this.currentPlanName;
    },
    capitalizedPlanName() {
      if (!this.displayCurrentPlanName) return '';
      return this.displayCurrentPlanName.charAt(0).toUpperCase() + this.displayCurrentPlanName.slice(1);
    },
    userInitials() {
      return this.userEmail ? this.userEmail.substring(0, 2).toUpperCase() : '';
    },
    isSpa() {
      // Check if we're in SPA mode by looking for the router
      return !!this.$router;
    },
    currentLocale() {
      // Get current locale from Vue Router params or default to 'cs'
      return this.$route?.params?.locale || 'cs';
    },
    localizedPath() {
      // Helper to create localized paths
      return (path) => {
        if (!path) return `/${this.currentLocale}`;
        // Remove leading slash if present and add locale prefix
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        return `/${this.currentLocale}/${cleanPath}`;
      };
    },
    localizedDestroySessionPath() {
      // Return the localized sign out path for Devise
      return `/${this.currentLocale}/users/sign_out`;
    },
    isAdminUser() {
      // Check if current user has admin access from store
      return this.$store.state.userStore.isAdmin || false
    }
  },
  methods: {
    handleNavItemClick() {
      // Close mobile menu when any navigation item is clicked
      if (this.isMobileMenuOpen) {
        this.closeMobileMenu();
      }
    },
    toggleSidebar() {
      this.isCollapsed = !this.isCollapsed;
      localStorage.setItem('sidebarCollapsed', this.isCollapsed);
    },
    async toggleWorkStatus() {
      if (this.isWorking) {
        await this.endWork();
      } else {
        await this.startWork();
      }
    },
    async startWork() {
      try {
        const createResponse = await axios.post('/daily_logs');
        const { daily_log, notifications } = createResponse.data;
        this.currentDailyLog = daily_log; 
        this.isWorking = true;
        if (notifications?.length) {
          notifications.forEach(notification => {
            sendFlashMessage(this.$t(notification.message, notification.message), notification.type);
          });
        } else {
           sendFlashMessage(this.$t('work_started_message', 'Práce byla zahájena.'), 'info');
        }
        // Optionally reload or emit event to sync other components
        // window.location.reload(); // Simple but disruptive
      } catch (error) {
        this.handleError(error, this.$t('start_work_error', 'Nepodařilo se zahájit práci'));
      }
    },
    async endWork() {
      if (!this.currentDailyLog?.id) {
        console.error('No current daily log ID available to end work.');
        // Attempt to fetch current status if ID is missing
        await this.checkCurrentStatus();
        if (!this.currentDailyLog?.id) {
           sendFlashMessage(this.$t('cannot_end_work_no_active_log', 'Nelze ukončit práci, aktivní záznam nenalezen.'), 'alert');
           this.isWorking = false; // Assume not working if no active log found
           return;
        }
      }

      try {
        const endResponse = await axios.put(`/daily_logs/${this.currentDailyLog.id}`, {
          daily_log: { end_time: new Date().toISOString() }
        });
        this.isWorking = false;
        this.currentDailyLog = null;
        if (endResponse.data.notifications?.length) {
          endResponse.data.notifications.forEach(notification => {
            sendFlashMessage(this.$t(notification.message, notification.message), notification.type);
          });
        } else {
          sendFlashMessage(this.$t('work_ended_message', 'Práce byla ukončena.'), 'info');
        }
         // Optionally reload or emit event to sync other components
        // window.location.reload(); // Simple but disruptive
      } catch (error) {
        this.handleError(error, this.$t('end_work_error', 'Nepodařilo se ukončit práci'));
      }
    },
    async checkCurrentStatus() {
      try {
        const response = await axios.get('/daily_logs/current_status');
        const { last_log } = response.data;
        if (last_log && !last_log.end_time) {
          this.currentDailyLog = last_log;
          this.isWorking = true;
        } else {
          this.isWorking = false;
          this.currentDailyLog = null;
        }
      } catch (error) {
        console.error('Error checking sidebar work status:', error);
        // Don't show error to user, might be transient network issue
        this.isWorking = false; // Default to not working on error
        this.currentDailyLog = null;
      }
    },
    handleError(error, defaultMessage) {
      const errorMessage = error.response?.data?.errors?.join('\\n') || defaultMessage;
      sendFlashMessage(errorMessage, 'alert');
    },
    openMobileMenu() {
      console.log('Sidebar: openMobileMenu called, setting isMobileMenuOpen to true.');
      this.isMobileMenuOpen = true;
    },
    closeMobileMenu() {
      console.log('Sidebar: closeMobileMenu called, setting isMobileMenuOpen to false.');
      this.isMobileMenuOpen = false;
    },
    handleToggleMobileSidebar() {
      console.log('Sidebar: Received toggle event, opening mobile menu.');
      this.openMobileMenu();
    },
    isCurrentPage(path) {
      return this.currentPagePath === path;
    },
    isCurrentController(controllerName) {
      return this.currentControllerName === controllerName;
    },
    isCurrentAction(actionName) {
      return this.currentActionName === actionName;
    },
    navigateTo(path) {
      if (this.isSpa) {
        // In SPA mode, use Vue Router
        this.$router.push(path);
      } else {
        // In legacy mode, use regular navigation
        window.location.href = path;
      }
    },
    async handleLogout() {
      try {
        // Use Vuex store for centralized logout logic, which calls the correct API endpoint
        await this.$store.dispatch('userStore/logout');
        
        const currentLocale = this.currentLocale;
        
        // Redirect to login using Vue Router, consistent with other components
        this.$router.push({ 
          name: 'login', // Assumes a 'login' route name exists
          params: { locale: currentLocale } 
        });
      } catch (error) {
        console.error('Logout failed in Sidebar:', error);
        // Fallback to full page reload on error
        const currentLocale = this.currentLocale;
        window.location.href = `/${currentLocale}/users/sign_in`;
      }
    }
  },
  async mounted() {
    this.isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    document.addEventListener('toggle-mobile-sidebar', this.handleToggleMobileSidebar);
    window.addEventListener('keydown', this.handleEscapeKey);
    this.checkCurrentStatus();
    
    // Load user data in SPA mode
    if (this.isSpa && this.$store) {
      try {
        await this.$store.dispatch('userStore/fetchUserData');
      } catch (error) {
        console.error('Failed to load user data in sidebar:', error);
      }
    }
  },
  beforeUnmount() {
    document.removeEventListener('toggle-mobile-sidebar', this.handleToggleMobileSidebar);
    window.removeEventListener('keydown', this.handleEscapeKey);
  },
  created() {
    this.handleEscapeKey = (e) => {
      if (e.key === 'Escape' && this.isMobileMenuOpen) {
        this.closeMobileMenu();
      }
    };
  }
};
</script>

<style scoped>
.trunc-email {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
}

.sidebar.collapsed {
  width: 65px; 
}

.sidebar.collapsed .sidebar-header {
   padding-left: 1rem; 
   padding-right: 1rem; 
}
</style> 